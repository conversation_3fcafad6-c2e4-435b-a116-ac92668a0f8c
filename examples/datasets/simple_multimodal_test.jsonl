{"_config": {"image_base_path": "./test_images", "default_image_quality": 85}}
{"messages": [{"role": "user", "content": [{"type": "text", "text": "Describe what you see in this image"}, {"type": "image", "path": "nature/landscape1.jpg"}]}], "expected_tokens": 80, "metadata": {"id": "test_001", "category": "image_description"}}
{"messages": [{"role": "user", "content": [{"type": "text", "text": "What objects can you identify in this room?"}, {"type": "image", "path": "objects/room1.jpg"}]}], "expected_tokens": 60, "metadata": {"id": "test_002", "category": "object_detection"}}
{"messages": [{"role": "user", "content": [{"type": "text", "text": "What is the mood or atmosphere of this scene?"}, {"type": "image", "path": "emotions/happy_scene.jpg"}]}], "expected_tokens": 70, "metadata": {"id": "test_003", "category": "emotion_analysis"}}
