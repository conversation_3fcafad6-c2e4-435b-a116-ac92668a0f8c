{"_config": {"image_base_path": "./test_images", "default_image_quality": 85}}
{"messages": [{"role": "user", "content": [{"type": "text", "text": "Describe what you see in this image"}, {"type": "image", "path": "nature/landscape1.jpg"}]}], "expected_tokens": 80, "metadata": {"id": "qwen_test_001", "category": "image_description", "test_type": "basic_vision"}}
{
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "What objects can you identify in this room?"},
        {"type": "image", "path": "objects/room1.jpg"}
      ]
    }
  ],
  "expected_tokens": 60,
  "metadata": {
    "id": "qwen_test_002",
    "category": "object_detection",
    "test_type": "object_recognition"
  }
}
{
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "Read and extract the text from this document"},
        {"type": "image", "path": "text/document1.jpg"}
      ]
    }
  ],
  "expected_tokens": 100,
  "metadata": {
    "id": "qwen_test_003",
    "category": "ocr",
    "test_type": "text_extraction"
  }
}
{
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "What is the mood or atmosphere of this scene?"},
        {"type": "image", "path": "emotions/happy_scene.jpg"}
      ]
    }
  ],
  "expected_tokens": 70,
  "metadata": {
    "id": "qwen_test_004",
    "category": "emotion_analysis",
    "test_type": "sentiment_detection"
  }
}
{
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "Compare these two images and tell me what's different"},
        {"type": "image", "path": "nature/landscape1.jpg"},
        {"type": "image", "path": "nature/landscape2.jpg"}
      ]
    }
  ],
  "expected_tokens": 120,
  "metadata": {
    "id": "qwen_test_005",
    "category": "image_comparison",
    "test_type": "multi_image_analysis"
  }
}
{
  "messages": [
    {
      "role": "system", "content": "You are a helpful assistant that can analyze images in detail."},
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "Analyze this image and provide a detailed description including colors, objects, and composition"},
        {"type": "image", "path": "objects/room2.jpg"}
      ]
    }
  ],
  "expected_tokens": 150,
  "metadata": {
    "id": "qwen_test_006",
    "category": "detailed_analysis",
    "test_type": "comprehensive_vision"
  }
}
{
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "What type of landscape is shown in this image?"},
        {"type": "image", "path": "nature/landscape3.jpg"}
      ]
    }
  ],
  "expected_tokens": 50,
  "metadata": {
    "id": "qwen_test_007",
    "category": "classification",
    "test_type": "scene_classification"
  }
}
{
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "Count how many distinct objects or shapes you can see in this image"},
        {"type": "image", "path": "objects/room3.jpg"}
      ]
    }
  ],
  "expected_tokens": 40,
  "metadata": {
    "id": "qwen_test_008",
    "category": "counting",
    "test_type": "object_counting"
  }
}
{
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "What colors are dominant in this image?"},
        {"type": "image", "path": "nature/landscape4.jpg"}
      ]
    }
  ],
  "expected_tokens": 30,
  "metadata": {
    "id": "qwen_test_009",
    "category": "color_analysis",
    "test_type": "color_detection"
  }
}
{
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "Is there any text visible in this image? If so, what does it say?"},
        {"type": "image", "path": "objects/room4.jpg"}
      ]
    }
  ],
  "expected_tokens": 60,
  "metadata": {
    "id": "qwen_test_010",
    "category": "text_detection",
    "test_type": "text_presence_check"
  }
}
