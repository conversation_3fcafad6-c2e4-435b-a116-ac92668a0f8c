# High performance testing configuration (TOML format)
model = "Qwen/Qwen2.5-VL-32B-Instruct"
api_base = "https://d07161945-vllm-openaiv092-463-c4nsjzqg-8000.550c.cloud/v1/chat/completions"
concurrency = 8
total_requests = 100
max_tokens = 200
temperature = 0.1
stream = true
timeout = 60
output_dir = "benchmark/performance_test"
verbose = false

# Optional dataset
# dataset = "examples/datasets/simple_text.jsonl"

# Multimodal settings (disabled for performance test)
multimodal = false
max_image_size = 1024
image_quality = 85
