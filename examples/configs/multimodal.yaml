# Multimodal model testing configuration
model: "Qwen/Qwen2.5-VL-32B-Instruct"
api_base: "https://d07161945-vllm-openaiv092-463-c4nsjzqg-8000.550c.cloud/v1/chat/completions"
concurrency: 2
total_requests: 10
dataset: "examples/datasets/simple_multimodal_test.jsonl"
max_tokens: 150
temperature: 0.1
stream: false
timeout: 60
output_dir: "benchmark"

# Multimodal specific settings
multimodal: true
image_base_path: "./test_images"
max_image_size: 1024
image_quality: 85

# Logging
verbose: true
