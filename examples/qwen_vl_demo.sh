#!/bin/bash

# Qwen2.5-VL Multimodal Stress Testing Demo
# This script demonstrates how to use stress_tool with Qwen2.5-VL model

set -e

echo "🚀 Qwen2.5-VL Multimodal Stress Testing Demo"
echo "============================================="

# Configuration
API_URL="https://d07161945-vllm-openaiv092-463-c4nsjzqg-8000.550c.cloud/v1/chat/completions"
MODEL="Qwen/Qwen2.5-VL-32B-Instruct"
DATASET_DIR="examples/datasets"
OUTPUT_DIR="benchmark"

echo "📋 Configuration:"
echo "  API URL: $API_URL"
echo "  Model: $MODEL"
echo "  Dataset Directory: $DATASET_DIR"
echo "  Output Directory: $OUTPUT_DIR"
echo ""

# Check if stress_tool binary exists
if [ ! -f "./target/debug/stress_tool" ]; then
    echo "❌ stress_tool binary not found. Building..."
    cargo build
    echo "✅ Build completed"
fi

echo "🔍 Testing API connectivity..."
curl -s -k -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "'$MODEL'",
    "messages": [{"role": "user", "content": "Hello"}],
    "max_tokens": 10
  }' > /dev/null && echo "✅ API is accessible" || echo "❌ API connection failed"

echo ""

# Test 1: Basic text-only test
echo "📝 Test 1: Basic Text-Only Test"
echo "--------------------------------"
./target/debug/stress_tool \
  --url "$API_URL" \
  --model "$MODEL" \
  --number 3 \
  --concurrency 1 \
  --max-tokens 50 \
  --verbose

echo ""

# Test 2: Multimodal test with simple dataset
echo "🖼️  Test 2: Multimodal Test (Simple Dataset)"
echo "--------------------------------------------"
./target/debug/stress_tool \
  --multimodal \
  --url "$API_URL" \
  --model "$MODEL" \
  --dataset "$DATASET_DIR/simple_multimodal_test.jsonl" \
  --number 2 \
  --concurrency 1 \
  --max-tokens 100 \
  --verbose

echo ""

# Test 3: Performance test with higher concurrency
echo "⚡ Test 3: Performance Test (Higher Concurrency)"
echo "-----------------------------------------------"
./target/debug/stress_tool \
  --url "$API_URL" \
  --model "$MODEL" \
  --number 10 \
  --concurrency 3 \
  --max-tokens 80 \
  --timeout 60 \
  --verbose

echo ""

# Test 4: Streaming test
echo "🌊 Test 4: Streaming Test"
echo "-------------------------"
./target/debug/stress_tool \
  --url "$API_URL" \
  --model "$MODEL" \
  --stream \
  --number 5 \
  --concurrency 2 \
  --max-tokens 100 \
  --verbose

echo ""

echo "✅ All tests completed!"
echo "📊 Results are available in the '$OUTPUT_DIR' directory"
echo ""
echo "💡 Tips:"
echo "  - Use --multimodal flag for vision model testing"
echo "  - Adjust --concurrency based on your server capacity"
echo "  - Use --stream for testing streaming responses"
echo "  - Check METRICS.md for detailed output format"
echo ""
echo "🔗 For more examples, see:"
echo "  - examples/datasets/ for sample datasets"
echo "  - docs/ for detailed documentation"
