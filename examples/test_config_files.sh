#!/bin/bash

# Configuration File Testing Script
# This script tests various configuration file scenarios

set -e

echo "🧪 Configuration File Testing"
echo "============================="

# Check if stress_tool binary exists
if [ ! -f "./target/debug/stress_tool" ]; then
    echo "❌ stress_tool binary not found. Building..."
    cargo build
    echo "✅ Build completed"
fi

echo ""

# Test 1: Basic YAML configuration
echo "📄 Test 1: Basic YAML Configuration"
echo "-----------------------------------"
echo "Testing: examples/configs/basic_text.yaml"
./target/debug/stress_tool --config-file examples/configs/basic_text.yaml --number 2
echo "✅ YAML config test passed"
echo ""

# Test 2: Multimodal YAML configuration
echo "🖼️  Test 2: Multimodal YAML Configuration"
echo "-----------------------------------------"
echo "Testing: examples/configs/multimodal.yaml"
./target/debug/stress_tool --config-file examples/configs/multimodal.yaml --number 1
echo "✅ Multimodal YAML config test passed"
echo ""

# Test 3: TOML configuration
echo "📋 Test 3: TOML Configuration"
echo "-----------------------------"
echo "Testing: examples/configs/performance.toml"
./target/debug/stress_tool --config-file examples/configs/performance.toml --number 2
echo "✅ TOML config test passed"
echo ""

# Test 4: Command line override
echo "⚡ Test 4: Command Line Override"
echo "-------------------------------"
echo "Testing: Config file + CLI overrides"
echo "Config file concurrency: 4, CLI override: --concurrency 1"
./target/debug/stress_tool --config-file examples/configs/basic_text.yaml --concurrency 1 --number 2 --verbose
echo "✅ CLI override test passed"
echo ""

# Test 5: Error handling - nonexistent file
echo "❌ Test 5: Error Handling - Nonexistent File"
echo "--------------------------------------------"
echo "Testing: nonexistent.yaml (should fail gracefully)"
if ./target/debug/stress_tool --config-file nonexistent.yaml 2>&1 | grep -q "Failed to read config file"; then
    echo "✅ Error handling test passed - correctly reported missing file"
else
    echo "❌ Error handling test failed"
    exit 1
fi
echo ""

# Test 6: Mixed configuration (config file + CLI URL)
echo "🔗 Test 6: Mixed Configuration"
echo "------------------------------"
echo "Testing: Config file without URL + CLI URL"
# Create a temporary config with placeholder URL
cat > /tmp/test_config.yaml << EOF
model: "test-model"
api_base: "http://placeholder"
concurrency: 2
total_requests: 5
max_tokens: 50
temperature: 0.1
stream: false
timeout: 30
output_dir: "benchmark"
multimodal: false
max_image_size: 1024
image_quality: 85
verbose: true
EOF

./target/debug/stress_tool --config-file /tmp/test_config.yaml --url "https://d07161945-vllm-openaiv092-463-c4nsjzqg-8000.550c.cloud/v1/chat/completions" --number 1
echo "✅ Mixed configuration test passed"
rm /tmp/test_config.yaml
echo ""

echo "🎉 All configuration file tests passed!"
echo ""
echo "📝 Summary:"
echo "  ✅ YAML configuration loading"
echo "  ✅ TOML configuration loading"
echo "  ✅ Multimodal configuration"
echo "  ✅ Command line parameter override"
echo "  ✅ Error handling for missing files"
echo "  ✅ Mixed configuration scenarios"
echo ""
echo "💡 Configuration file features:"
echo "  - Support for YAML (.yaml, .yml) and TOML (.toml) formats"
echo "  - Command line arguments override config file values"
echo "  - Comprehensive error messages for invalid configs"
echo "  - All configuration options available in files"
echo ""
echo "📖 Usage examples:"
echo "  ./stress_tool --config-file config.yaml"
echo "  ./stress_tool --config-file config.toml --concurrency 8"
echo "  ./stress_tool --config-file config.yaml --url https://api.example.com"
