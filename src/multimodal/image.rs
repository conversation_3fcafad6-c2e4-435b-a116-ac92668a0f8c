use super::{MultimodalError, Result};
use base64::{engine::general_purpose::STANDARD, Engine as _};
use image::{DynamicImage, ImageFormat, GenericImageView};
use std::io::Cursor;
use std::path::Path;
use std::time::Instant;

/// Image processor for handling various image formats and operations
#[derive(Clone)]
pub struct ImageProcessor {
    max_size: u32,
    quality: u8,
}

impl ImageProcessor {
    pub fn new(max_size: u32, quality: u8) -> Self {
        Self { max_size, quality }
    }
    
    /// Process an image file and return base64 encoded data with timing info
    pub fn process_image_file(&self, path: &Path) -> Result<(String, u64, u64)> {
        let start = Instant::now();
        
        if !path.exists() {
            return Err(MultimodalError::FileNotFound(path.to_path_buf()));
        }
        
        // Get file size
        let file_size = std::fs::metadata(path)?.len();
        
        // Load and process image
        let img = image::open(path).map_err(|e| MultimodalError::ImageError(e.to_string()))?;
        let processed_img = self.resize_image(img)?;
        
        // Encode to base64
        let base64_data = self.encode_image_to_base64(&processed_img, path)?;
        
        let processing_time = start.elapsed().as_millis() as u64;
        
        Ok((base64_data, processing_time, file_size))
    }
    
    /// Process base64 image data and return processed base64 data
    pub fn process_base64_image(&self, base64_data: &str) -> Result<(String, u64)> {
        let start = Instant::now();
        
        // Decode base64
        let image_data = self.decode_base64_image(base64_data)?;
        
        // Load image from bytes
        let img = image::load_from_memory(&image_data).map_err(|e| MultimodalError::ImageError(e.to_string()))?;
        
        // Resize if needed
        let processed_img = self.resize_image(img)?;
        
        // Re-encode to base64
        let format = self.detect_format_from_base64(base64_data);
        let base64_result = self.encode_image_to_base64_with_format(&processed_img, format)?;
        
        let processing_time = start.elapsed().as_millis() as u64;
        
        Ok((base64_result, processing_time))
    }
    
    /// Resize image if it exceeds maximum dimensions
    fn resize_image(&self, img: DynamicImage) -> Result<DynamicImage> {
        let (width, height) = img.dimensions();
        
        if width <= self.max_size && height <= self.max_size {
            return Ok(img);
        }
        
        // Calculate new dimensions maintaining aspect ratio
        let (new_width, new_height) = if width > height {
            let ratio = self.max_size as f32 / width as f32;
            (self.max_size, (height as f32 * ratio) as u32)
        } else {
            let ratio = self.max_size as f32 / height as f32;
            ((width as f32 * ratio) as u32, self.max_size)
        };
        
        Ok(img.resize(new_width, new_height, image::imageops::FilterType::Lanczos3))
    }
    
    /// Encode image to base64 with format detection from file path
    fn encode_image_to_base64(&self, img: &DynamicImage, path: &Path) -> Result<String> {
        let format = self.detect_format_from_path(path);
        self.encode_image_to_base64_with_format(img, format)
    }
    
    /// Encode image to base64 with specified format
    fn encode_image_to_base64_with_format(&self, img: &DynamicImage, format: ImageFormat) -> Result<String> {
        let mut buffer = Vec::new();
        let mut cursor = Cursor::new(&mut buffer);
        
        match format {
            ImageFormat::Jpeg => {
                let encoder = image::codecs::jpeg::JpegEncoder::new_with_quality(&mut cursor, self.quality);
                img.write_with_encoder(encoder).map_err(|e| MultimodalError::ImageError(e.to_string()))?;
            }
            ImageFormat::Png => {
                img.write_to(&mut cursor, format).map_err(|e| MultimodalError::ImageError(e.to_string()))?;
            }
            ImageFormat::WebP => {
                img.write_to(&mut cursor, format).map_err(|e| MultimodalError::ImageError(e.to_string()))?;
            }
            _ => {
                // Default to JPEG for other formats
                let encoder = image::codecs::jpeg::JpegEncoder::new_with_quality(&mut cursor, self.quality);
                img.write_with_encoder(encoder).map_err(|e| MultimodalError::ImageError(e.to_string()))?;
            }
        }
        
        let mime_type = match format {
            ImageFormat::Jpeg => "image/jpeg",
            ImageFormat::Png => "image/png",
            ImageFormat::WebP => "image/webp",
            _ => "image/jpeg",
        };
        
        let base64_data = STANDARD.encode(&buffer);
        Ok(format!("data:{};base64,{}", mime_type, base64_data))
    }
    
    /// Detect image format from file extension
    fn detect_format_from_path(&self, path: &Path) -> ImageFormat {
        match path.extension().and_then(|ext| ext.to_str()) {
            Some("jpg") | Some("jpeg") => ImageFormat::Jpeg,
            Some("png") => ImageFormat::Png,
            Some("webp") => ImageFormat::WebP,
            Some("bmp") => ImageFormat::Bmp,
            Some("tiff") | Some("tif") => ImageFormat::Tiff,
            _ => ImageFormat::Jpeg, // Default to JPEG
        }
    }
    
    /// Detect image format from base64 data URL
    fn detect_format_from_base64(&self, base64_data: &str) -> ImageFormat {
        if base64_data.starts_with("data:image/png") {
            ImageFormat::Png
        } else if base64_data.starts_with("data:image/webp") {
            ImageFormat::WebP
        } else if base64_data.starts_with("data:image/bmp") {
            ImageFormat::Bmp
        } else {
            ImageFormat::Jpeg // Default to JPEG
        }
    }
    
    /// Decode base64 image data
    fn decode_base64_image(&self, base64_data: &str) -> Result<Vec<u8>> {
        // Handle data URL format
        let data = if base64_data.starts_with("data:") {
            let parts: Vec<&str> = base64_data.split(',').collect();
            if parts.len() != 2 {
                return Err(MultimodalError::ConfigError("Invalid base64 data URL format".to_string()));
            }
            parts[1]
        } else {
            base64_data
        };
        
        STANDARD.decode(data).map_err(MultimodalError::Base64Error)
    }
    
    /// Validate if file is a supported image format
    pub fn is_supported_format(path: &Path) -> bool {
        match path.extension().and_then(|ext| ext.to_str()) {
            Some("jpg") | Some("jpeg") | Some("png") | Some("webp") | Some("bmp") | Some("tiff") | Some("tif") => true,
            _ => false,
        }
    }
    
    /// Get image dimensions without loading the full image
    pub fn get_image_dimensions(path: &Path) -> Result<(u32, u32)> {
        let reader = image::io::Reader::open(path).map_err(|e| MultimodalError::ImageError(e.to_string()))?;
        let dimensions = reader.into_dimensions().map_err(|e| MultimodalError::ImageError(e.to_string()))?;
        Ok(dimensions)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::io::Write;
    use tempfile::NamedTempFile;
    
    fn create_test_image() -> NamedTempFile {
        let img = DynamicImage::new_rgb8(100, 100);
        let mut file = NamedTempFile::new().unwrap();
        img.save_with_format(&mut file, ImageFormat::Png).unwrap();
        file.flush().unwrap();
        file
    }
    
    #[test]
    fn test_image_processor_creation() {
        let processor = ImageProcessor::new(1024, 85);
        assert_eq!(processor.max_size, 1024);
        assert_eq!(processor.quality, 85);
    }
    
    #[test]
    fn test_format_detection() {
        let processor = ImageProcessor::new(1024, 85);
        
        assert_eq!(processor.detect_format_from_path(Path::new("test.jpg")), ImageFormat::Jpeg);
        assert_eq!(processor.detect_format_from_path(Path::new("test.png")), ImageFormat::Png);
        assert_eq!(processor.detect_format_from_path(Path::new("test.webp")), ImageFormat::WebP);
        assert_eq!(processor.detect_format_from_path(Path::new("test.unknown")), ImageFormat::Jpeg);
    }
    
    #[test]
    fn test_supported_format() {
        assert!(ImageProcessor::is_supported_format(Path::new("test.jpg")));
        assert!(ImageProcessor::is_supported_format(Path::new("test.png")));
        assert!(!ImageProcessor::is_supported_format(Path::new("test.txt")));
    }
    
    #[test]
    fn test_resize_image() {
        let processor = ImageProcessor::new(50, 85);
        let img = DynamicImage::new_rgb8(100, 100);
        
        let resized = processor.resize_image(img).unwrap();
        let (width, height) = resized.dimensions();
        
        assert!(width <= 50);
        assert!(height <= 50);
    }
    
    #[test]
    fn test_process_image_file() {
        let processor = ImageProcessor::new(1024, 85);
        let test_file = create_test_image();
        
        let result = processor.process_image_file(test_file.path());
        assert!(result.is_ok());
        
        let (base64_data, processing_time, file_size) = result.unwrap();
        assert!(base64_data.starts_with("data:image/"));
        assert!(processing_time > 0);
        assert!(file_size > 0);
    }
    
    #[test]
    fn test_file_not_found() {
        let processor = ImageProcessor::new(1024, 85);
        let result = processor.process_image_file(Path::new("nonexistent.jpg"));
        
        assert!(matches!(result, Err(MultimodalError::FileNotFound(_))));
    }
}
