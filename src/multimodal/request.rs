use super::{
    ContentType, DatasetEntry, Message, MessageContent, MultimodalError,
    MultimodalStats, Result,
};
use super::image::ImageProcessor;
use crate::config::Config;
use serde_json::{json, Value};
use std::collections::HashMap;

use std::sync::Arc;
use tokio::sync::Mutex;

/// Request builder for multimodal API calls
#[derive(Clone)]
pub struct MultimodalRequestBuilder {
    config: Config,
    image_processor: ImageProcessor,
    stats: Arc<Mutex<MultimodalStats>>,
    cache: Arc<Mutex<HashMap<String, String>>>, // Path -> Base64 cache
}

impl MultimodalRequestBuilder {
    pub fn new(config: Config) -> Self {
        let image_processor = ImageProcessor::new(config.max_image_size, config.image_quality);
        
        Self {
            config,
            image_processor,
            stats: Arc::new(Mutex::new(MultimodalStats::default())),
            cache: Arc::new(Mutex::new(HashMap::new())),
        }
    }
    
    /// Build API request from dataset entry
    pub async fn build_request(&self, entry: &DatasetEntry) -> Result<Value> {
        match (&entry.prompt, &entry.messages) {
            (Some(prompt), None) => {
                // Simple text prompt
                self.build_text_request(prompt).await
            }
            (None, Some(messages)) => {
                // Multimodal messages
                self.build_multimodal_request(messages).await
            }
            (Some(_), Some(_)) => {
                Err(MultimodalError::ConfigError(
                    "Dataset entry cannot have both 'prompt' and 'messages'".to_string(),
                ))
            }
            (None, None) => {
                Err(MultimodalError::ConfigError(
                    "Dataset entry must have either 'prompt' or 'messages'".to_string(),
                ))
            }
        }
    }
    
    /// Build simple text request
    async fn build_text_request(&self, prompt: &str) -> Result<Value> {
        Ok(json!({
            "model": self.config.model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": self.config.max_tokens,
            "temperature": self.config.temperature,
            "stream": self.config.stream
        }))
    }
    
    /// Build multimodal request with processed content
    async fn build_multimodal_request(&self, messages: &[Message]) -> Result<Value> {
        let mut processed_messages = Vec::new();
        
        for message in messages {
            let processed_content = match &message.content {
                MessageContent::Text(text) => json!(text),
                MessageContent::Array(content_array) => {
                    let mut processed_array = Vec::new();
                    
                    for content in content_array {
                        let processed = self.process_content_type(content).await?;
                        processed_array.push(processed);
                    }
                    
                    json!(processed_array)
                }
            };
            
            processed_messages.push(json!({
                "role": message.role,
                "content": processed_content
            }));
        }
        
        Ok(json!({
            "model": self.config.model,
            "messages": processed_messages,
            "max_tokens": self.config.max_tokens,
            "temperature": self.config.temperature,
            "stream": self.config.stream
        }))
    }
    
    /// Process individual content type
    async fn process_content_type(&self, content: &ContentType) -> Result<Value> {
        match content {
            ContentType::Text { text } => {
                Ok(json!({
                    "type": "text",
                    "text": text
                }))
            }
            ContentType::Image { path, description: _, resize: _ } => {
                let base64_data = self.process_image_path(path).await?;
                Ok(json!({
                    "type": "image_url",
                    "image_url": {
                        "url": base64_data
                    }
                }))
            }
            ContentType::ImageUrl { image_url } => {
                let processed_data = if image_url.url.starts_with("data:") {
                    // Already base64, but might need processing
                    let (processed, processing_time) = self.image_processor.process_base64_image(&image_url.url)?;
                    
                    let mut stats = self.stats.lock().await;
                    stats.add_image_processing(processing_time, 0); // Size unknown for base64
                    
                    processed
                } else {
                    image_url.url.clone()
                };
                
                Ok(json!({
                    "type": "image_url",
                    "image_url": {
                        "url": processed_data
                    }
                }))
            }
            #[cfg(feature = "video")]
            ContentType::Video { path, description: _, options } => {
                let frames = self.process_video_path(path, options).await?;
                
                // For now, convert video to multiple image frames
                // This is a simplified approach - real implementation might use video-specific APIs
                let mut frame_objects = Vec::new();
                for frame_base64 in frames {
                    frame_objects.push(json!({
                        "type": "image_url",
                        "image_url": {
                            "url": frame_base64
                        }
                    }));
                }
                
                // Return as array of images for now
                // Note: This might need adjustment based on the specific API requirements
                Ok(json!(frame_objects))
            }
        }
    }
    
    /// Process image file with caching
    async fn process_image_path(&self, path: &str) -> Result<String> {
        // Check cache first
        {
            let cache = self.cache.lock().await;
            if let Some(cached) = cache.get(path) {
                return Ok(cached.clone());
            }
        }
        
        // Resolve path
        let resolved_path = self.config.resolve_image_path(path);
        
        // Process image
        let (base64_data, processing_time, file_size) = 
            self.image_processor.process_image_file(&resolved_path)?;
        
        // Update stats
        {
            let mut stats = self.stats.lock().await;
            stats.add_image_processing(processing_time, file_size);
        }
        
        // Cache result
        {
            let mut cache = self.cache.lock().await;
            cache.insert(path.to_string(), base64_data.clone());
        }
        
        Ok(base64_data)
    }
    
    #[cfg(feature = "video")]
    async fn process_video_path(&self, path: &str, options: &Option<super::VideoOptions>) -> Result<Vec<String>> {
        use super::video::VideoProcessor;
        
        let resolved_path = self.config.resolve_video_path(path);
        let video_processor = VideoProcessor::new();
        
        let fps = options.as_ref().and_then(|o| o.fps).unwrap_or(1.0);
        let max_frames = options.as_ref().and_then(|o| o.max_frames).unwrap_or(10);
        let start_time = options.as_ref().and_then(|o| o.start_time).unwrap_or(0.0);
        let duration = options.as_ref().and_then(|o| o.duration);
        
        let (frames, processing_time, file_size) = video_processor
            .extract_frames(&resolved_path, fps, max_frames, start_time, duration)
            .await?;
        
        // Update stats
        {
            let mut stats = self.stats.lock().await;
            stats.add_video_processing(processing_time, file_size);
        }
        
        Ok(frames)
    }
    
    #[cfg(not(feature = "video"))]
    async fn process_video_path(&self, _path: &str, _options: &Option<()>) -> Result<Vec<String>> {
        Err(MultimodalError::ConfigError(
            "Video processing not enabled. Compile with --features video".to_string(),
        ))
    }
    
    /// Get current multimodal processing statistics
    pub async fn get_stats(&self) -> MultimodalStats {
        self.stats.lock().await.clone()
    }
    
    /// Clear cache to free memory
    pub async fn clear_cache(&self) {
        let mut cache = self.cache.lock().await;
        cache.clear();
    }
    
    /// Validate dataset entry
    pub fn validate_entry(&self, entry: &DatasetEntry) -> Result<()> {
        match (&entry.prompt, &entry.messages) {
            (Some(_), Some(_)) => {
                return Err(MultimodalError::ConfigError(
                    "Dataset entry cannot have both 'prompt' and 'messages'".to_string(),
                ));
            }
            (None, None) => {
                return Err(MultimodalError::ConfigError(
                    "Dataset entry must have either 'prompt' or 'messages'".to_string(),
                ));
            }
            _ => {}
        }
        
        if let Some(messages) = &entry.messages {
            for message in messages {
                self.validate_message(message)?;
            }
        }
        
        Ok(())
    }
    
    /// Validate individual message
    fn validate_message(&self, message: &Message) -> Result<()> {
        match &message.content {
            MessageContent::Text(_) => Ok(()),
            MessageContent::Array(content_array) => {
                for content in content_array {
                    self.validate_content_type(content)?;
                }
                Ok(())
            }
        }
    }
    
    /// Validate content type
    fn validate_content_type(&self, content: &ContentType) -> Result<()> {
        match content {
            ContentType::Text { .. } => Ok(()),
            ContentType::Image { path, .. } => {
                let resolved_path = self.config.resolve_image_path(path);
                if !resolved_path.exists() {
                    return Err(MultimodalError::FileNotFound(resolved_path));
                }
                if !ImageProcessor::is_supported_format(&resolved_path) {
                    return Err(MultimodalError::UnsupportedFormat(
                        format!("Image format not supported: {}", path)
                    ));
                }
                Ok(())
            }
            ContentType::ImageUrl { .. } => Ok(()),
            #[cfg(feature = "video")]
            ContentType::Video { path, .. } => {
                let resolved_path = self.config.resolve_video_path(path);
                if !resolved_path.exists() {
                    return Err(MultimodalError::FileNotFound(resolved_path));
                }
                // Add video format validation here
                Ok(())
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;
    use std::fs;
    
    fn create_test_config() -> Config {
        Config {
            model: "test-model".to_string(),
            api_base: "http://localhost:8000".to_string(),
            concurrency: 1,
            total_requests: 1,
            dataset: None,
            max_tokens: 100,
            temperature: 0.1,
            stream: false,
            timeout: 30,
            output_dir: tempdir().unwrap().into_path(),
            multimodal: true,
            image_base_path: None,
            video_base_path: None,
            max_image_size: 1024,
            image_quality: 85,
            api_key: None,
            verbose: false,
        }
    }
    
    #[tokio::test]
    async fn test_build_text_request() {
        let config = create_test_config();
        let builder = MultimodalRequestBuilder::new(config);
        
        let entry = DatasetEntry {
            prompt: Some("Hello, world!".to_string()),
            messages: None,
            expected_tokens: Some(50),
            metadata: None,
            _config: None,
        };
        
        let request = builder.build_request(&entry).await.unwrap();
        assert_eq!(request["model"], "test-model");
        assert_eq!(request["messages"][0]["content"], "Hello, world!");
    }
    
    #[tokio::test]
    async fn test_validate_entry() {
        let config = create_test_config();
        let builder = MultimodalRequestBuilder::new(config);
        
        // Valid text entry
        let entry = DatasetEntry {
            prompt: Some("Hello".to_string()),
            messages: None,
            expected_tokens: Some(50),
            metadata: None,
            _config: None,
        };
        assert!(builder.validate_entry(&entry).is_ok());
        
        // Invalid entry with both prompt and messages
        let entry = DatasetEntry {
            prompt: Some("Hello".to_string()),
            messages: Some(vec![]),
            expected_tokens: Some(50),
            metadata: None,
            _config: None,
        };
        assert!(builder.validate_entry(&entry).is_err());
    }
}
