use crate::config::Config;
use anyhow::{Context, Result};
use reqwest::Client;
use serde_json::Value;
use std::time::{Duration, Instant};
use tokio::time::timeout;
use tracing::{debug, warn};

/// HTTP client for making API requests
#[derive(Clone)]
pub struct ApiClient {
    client: Client,
    config: Config,
}

impl ApiClient {
    pub fn new(config: Config) -> Result<Self> {
        let mut headers = reqwest::header::HeaderMap::new();
        headers.insert(
            reqwest::header::CONTENT_TYPE,
            reqwest::header::HeaderValue::from_static("application/json"),
        );
        
        if let Some(api_key) = &config.api_key {
            let auth_value = format!("Bearer {}", api_key);
            headers.insert(
                reqwest::header::AUTHORIZATION,
                reqwest::header::HeaderValue::from_str(&auth_value)
                    .context("Invalid API key format")?,
            );
        }
        
        let client = Client::builder()
            .default_headers(headers)
            .timeout(Duration::from_secs(config.timeout))
            .build()
            .context("Failed to create HTTP client")?;
        
        Ok(Self { client, config })
    }
    
    /// Send a request and measure response time
    pub async fn send_request(&self, request_body: Value) -> Result<ApiResponse> {
        let start_time = Instant::now();
        
        debug!("Sending request to: {}", self.config.api_base);
        debug!("Request body: {}", serde_json::to_string_pretty(&request_body)?);
        
        let response = timeout(
            Duration::from_secs(self.config.timeout),
            self.client
                .post(&self.config.api_base)
                .json(&request_body)
                .send()
        )
        .await
        .context("Request timeout")?
        .context("Failed to send request")?;
        
        let status = response.status();
        let headers = response.headers().clone();
        
        if !status.is_success() {
            let error_text = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            warn!("Request failed with status {}: {}", status, error_text);
            return Ok(ApiResponse {
                success: false,
                status_code: status.as_u16(),
                response_time: start_time.elapsed(),
                content: None,
                error: Some(format!("HTTP {}: {}", status, error_text)),
                headers,
            });
        }
        
        let response_text = response.text().await
            .context("Failed to read response body")?;
        
        debug!("Response: {}", response_text);
        
        let content: Value = serde_json::from_str(&response_text)
            .context("Failed to parse JSON response")?;
        
        Ok(ApiResponse {
            success: true,
            status_code: status.as_u16(),
            response_time: start_time.elapsed(),
            content: Some(content),
            error: None,
            headers,
        })
    }
    
    /// Send a streaming request
    pub async fn send_streaming_request(&self, request_body: Value) -> Result<StreamingResponse> {
        let start_time = Instant::now();
        
        debug!("Sending streaming request to: {}", self.config.api_base);
        
        let response = timeout(
            Duration::from_secs(self.config.timeout),
            self.client
                .post(&self.config.api_base)
                .json(&request_body)
                .send()
        )
        .await
        .context("Request timeout")?
        .context("Failed to send request")?;
        
        let status = response.status();
        let headers = response.headers().clone();
        
        if !status.is_success() {
            let error_text = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            return Ok(StreamingResponse {
                success: false,
                status_code: status.as_u16(),
                start_time,
                first_chunk_time: None,
                last_chunk_time: None,
                chunks: Vec::new(),
                error: Some(format!("HTTP {}: {}", status, error_text)),
                headers,
            });
        }
        
        // Process streaming response
        let mut chunks = Vec::new();
        let mut first_chunk_time = None;
        let mut last_chunk_time = None;
        
        let mut stream = response.bytes_stream();
        use futures::StreamExt;
        
        while let Some(chunk_result) = stream.next().await {
            let chunk_time = Instant::now();
            
            match chunk_result {
                Ok(chunk) => {
                    if first_chunk_time.is_none() {
                        first_chunk_time = Some(chunk_time);
                    }
                    last_chunk_time = Some(chunk_time);
                    
                    let chunk_str = String::from_utf8_lossy(&chunk);
                    
                    // Parse SSE chunks
                    for line in chunk_str.lines() {
                        if line.starts_with("data: ") {
                            let data = &line[6..]; // Remove "data: " prefix
                            if data == "[DONE]" {
                                break;
                            }
                            
                            if let Ok(json) = serde_json::from_str::<Value>(data) {
                                chunks.push(StreamChunk {
                                    timestamp: chunk_time,
                                    data: json,
                                });
                            }
                        }
                    }
                }
                Err(e) => {
                    warn!("Error reading chunk: {}", e);
                    break;
                }
            }
        }
        
        Ok(StreamingResponse {
            success: true,
            status_code: status.as_u16(),
            start_time,
            first_chunk_time,
            last_chunk_time,
            chunks,
            error: None,
            headers,
        })
    }
}

/// Response from a non-streaming API call
#[derive(Debug)]
pub struct ApiResponse {
    pub success: bool,
    pub status_code: u16,
    pub response_time: Duration,
    pub content: Option<Value>,
    pub error: Option<String>,
    pub headers: reqwest::header::HeaderMap,
}

impl ApiResponse {
    /// Extract completion text from response
    pub fn get_completion_text(&self) -> Option<String> {
        self.content.as_ref().and_then(|content| {
            content["choices"][0]["message"]["content"]
                .as_str()
                .map(|s| s.to_string())
        })
    }
    
    /// Extract token usage information
    pub fn get_token_usage(&self) -> Option<TokenUsage> {
        self.content.as_ref().and_then(|content| {
            let usage = &content["usage"];
            Some(TokenUsage {
                prompt_tokens: usage["prompt_tokens"].as_u64()?,
                completion_tokens: usage["completion_tokens"].as_u64()?,
                total_tokens: usage["total_tokens"].as_u64()?,
            })
        })
    }
}

/// Response from a streaming API call
#[derive(Debug)]
pub struct StreamingResponse {
    pub success: bool,
    pub status_code: u16,
    pub start_time: Instant,
    pub first_chunk_time: Option<Instant>,
    pub last_chunk_time: Option<Instant>,
    pub chunks: Vec<StreamChunk>,
    pub error: Option<String>,
    pub headers: reqwest::header::HeaderMap,
}

impl StreamingResponse {
    /// Get time to first token
    pub fn time_to_first_token(&self) -> Option<Duration> {
        self.first_chunk_time.map(|first| first.duration_since(self.start_time))
    }
    
    /// Get total response time
    pub fn total_response_time(&self) -> Duration {
        self.last_chunk_time
            .unwrap_or(self.start_time)
            .duration_since(self.start_time)
    }
    
    /// Get generation time (first token to last token)
    pub fn generation_time(&self) -> Option<Duration> {
        match (self.first_chunk_time, self.last_chunk_time) {
            (Some(first), Some(last)) => Some(last.duration_since(first)),
            _ => None,
        }
    }
    
    /// Extract complete text from all chunks
    pub fn get_completion_text(&self) -> String {
        let mut text = String::new();
        
        for chunk in &self.chunks {
            if let Some(delta) = chunk.data["choices"][0]["delta"]["content"].as_str() {
                text.push_str(delta);
            }
        }
        
        text
    }
    
    /// Count completion tokens (approximate)
    pub fn count_completion_tokens(&self) -> usize {
        self.chunks.len() // Simplified - each chunk roughly represents one token
    }
}

/// Individual chunk from streaming response
#[derive(Debug)]
pub struct StreamChunk {
    pub timestamp: Instant,
    pub data: Value,
}

/// Token usage information
#[derive(Debug, Clone)]
pub struct TokenUsage {
    pub prompt_tokens: u64,
    pub completion_tokens: u64,
    pub total_tokens: u64,
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;
    
    fn create_test_config() -> Config {
        Config {
            model: "test-model".to_string(),
            api_base: "http://localhost:8000/v1/chat/completions".to_string(),
            concurrency: 1,
            total_requests: 1,
            dataset: None,
            max_tokens: 100,
            temperature: 0.1,
            stream: false,
            timeout: 30,
            output_dir: std::path::PathBuf::from("test"),
            multimodal: false,
            image_base_path: None,
            video_base_path: None,
            max_image_size: 1024,
            image_quality: 85,
            api_key: Some("test-key".to_string()),
            verbose: false,
        }
    }
    
    #[test]
    fn test_client_creation() {
        let config = create_test_config();
        let client = ApiClient::new(config);
        assert!(client.is_ok());
    }
    
    #[test]
    fn test_response_text_extraction() {
        let response = ApiResponse {
            success: true,
            status_code: 200,
            response_time: Duration::from_millis(100),
            content: Some(json!({
                "choices": [{
                    "message": {
                        "content": "Hello, world!"
                    }
                }]
            })),
            error: None,
            headers: reqwest::header::HeaderMap::new(),
        };
        
        assert_eq!(response.get_completion_text(), Some("Hello, world!".to_string()));
    }
    
    #[test]
    fn test_token_usage_extraction() {
        let response = ApiResponse {
            success: true,
            status_code: 200,
            response_time: Duration::from_millis(100),
            content: Some(json!({
                "usage": {
                    "prompt_tokens": 10,
                    "completion_tokens": 20,
                    "total_tokens": 30
                }
            })),
            error: None,
            headers: reqwest::header::HeaderMap::new(),
        };
        
        let usage = response.get_token_usage().unwrap();
        assert_eq!(usage.prompt_tokens, 10);
        assert_eq!(usage.completion_tokens, 20);
        assert_eq!(usage.total_tokens, 30);
    }
}
