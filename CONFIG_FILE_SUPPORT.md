# Configuration File Support

## Overview

stress_tool now supports configuration files in both YAML and TOML formats, providing a convenient way to manage complex configurations and avoid long command lines.

## ✅ Implemented Features

### 1. File Format Support
- **YAML**: `.yaml` and `.yml` extensions
- **TOML**: `.toml` extension
- **Auto-detection**: Format determined by file extension

### 2. Configuration Loading
- **File-first approach**: Load base configuration from file
- **CLI override**: Command line arguments take precedence
- **Validation**: Comprehensive validation with clear error messages

### 3. All Parameters Supported
All command line options are available in configuration files:
- Basic settings (model, api_base, concurrency, etc.)
- Multimodal settings (image_base_path, max_image_size, etc.)
- Advanced options (timeout, temperature, stream, etc.)

## 📁 Configuration File Examples

### Basic YAML Configuration
```yaml
# examples/configs/basic_text.yaml
model: "Qwen/Qwen2.5-VL-32B-Instruct"
api_base: "https://api.example.com/v1/chat/completions"
concurrency: 4
total_requests: 20
max_tokens: 100
temperature: 0.1
stream: false
timeout: 30
output_dir: "benchmark"
multimodal: false
max_image_size: 1024
image_quality: 85
verbose: true
```

### Multimodal YAML Configuration
```yaml
# examples/configs/multimodal.yaml
model: "Qwen/Qwen2.5-VL-32B-Instruct"
api_base: "https://api.example.com/v1/chat/completions"
concurrency: 2
total_requests: 10
dataset: "examples/datasets/simple_multimodal_test.jsonl"
max_tokens: 150
temperature: 0.1
stream: false
timeout: 60
output_dir: "benchmark"

# Multimodal specific settings
multimodal: true
image_base_path: "./test_images"
max_image_size: 1024
image_quality: 85

# Logging
verbose: true
```

### TOML Configuration
```toml
# examples/configs/performance.toml
model = "Qwen/Qwen2.5-VL-32B-Instruct"
api_base = "https://api.example.com/v1/chat/completions"
concurrency = 8
total_requests = 100
max_tokens = 200
temperature = 0.1
stream = true
timeout = 60
output_dir = "benchmark/performance_test"
verbose = false

multimodal = false
max_image_size = 1024
image_quality = 85
```

## 🚀 Usage Examples

### Basic Usage
```bash
# Use YAML configuration
./stress_tool --config-file examples/configs/basic_text.yaml

# Use TOML configuration
./stress_tool --config-file examples/configs/performance.toml
```

### CLI Override
```bash
# Override concurrency and request count
./stress_tool --config-file config.yaml --concurrency 8 --number 200

# Override URL for different environment
./stress_tool --config-file config.yaml --url https://staging-api.example.com

# Add verbose logging
./stress_tool --config-file config.yaml --verbose
```

### Mixed Configuration
```bash
# Config file provides base settings, CLI provides environment-specific overrides
./stress_tool --config-file base_config.yaml \
              --url $API_ENDPOINT \
              --api-key $API_KEY \
              --concurrency $CONCURRENCY
```

## 🔧 Configuration Priority

The configuration system follows a clear priority order:

1. **Command Line Arguments** (highest priority)
2. **Configuration File Values**
3. **Default Values** (lowest priority)

This allows for flexible configuration management where:
- Base settings are defined in config files
- Environment-specific overrides come from CLI
- Sensible defaults are always available

## ✅ Testing Results

All configuration file features have been thoroughly tested:

### Test Scenarios
- ✅ **YAML Loading**: Basic and complex YAML configurations
- ✅ **TOML Loading**: TOML format configurations
- ✅ **CLI Override**: Command line arguments override config file values
- ✅ **Error Handling**: Missing files, invalid syntax, validation errors
- ✅ **Mixed Configuration**: Config file + CLI combinations
- ✅ **Multimodal Support**: All multimodal options work in config files

### Test Script
Run the comprehensive test suite:
```bash
./examples/test_config_files.sh
```

## 🛠️ Implementation Details

### Key Components
- **Config Loading**: `Config::load_from_file()` handles YAML/TOML parsing
- **CLI Integration**: `Config::from_args()` merges file and CLI configurations
- **Override Logic**: `apply_args_overrides()` implements priority system
- **Validation**: Enhanced validation with file-specific error messages

### Error Handling
- **File Not Found**: Clear error message with file path
- **Parse Errors**: Format-specific error messages (YAML/TOML)
- **Validation Errors**: Field-level validation with helpful hints
- **Graceful Fallback**: Continue with CLI-only mode if file loading fails

## 📖 Best Practices

### Configuration Management
1. **Base Configuration**: Use config files for stable, environment-independent settings
2. **Environment Overrides**: Use CLI arguments for environment-specific values
3. **Secrets**: Pass sensitive data (API keys) via CLI or environment variables
4. **Documentation**: Comment your config files for team collaboration

### File Organization
```
configs/
├── base.yaml              # Common settings
├── development.yaml       # Dev environment
├── staging.yaml          # Staging environment
├── production.yaml       # Production environment
└── multimodal/
    ├── vision_test.yaml  # Vision model testing
    └── performance.yaml  # Performance benchmarks
```

### Usage Patterns
```bash
# Development
./stress_tool --config-file configs/development.yaml

# Staging with override
./stress_tool --config-file configs/staging.yaml --concurrency 4

# Production with secrets
./stress_tool --config-file configs/production.yaml --api-key $PROD_API_KEY
```

## 🔮 Future Enhancements

### Planned Features
- **Environment Variable Substitution**: `${API_KEY}` in config files
- **Config Validation**: Schema validation for config files
- **Config Generation**: Generate config files from CLI usage
- **Profile Support**: Multiple named configurations in one file

### Potential Improvements
- **JSON Support**: Add JSON configuration format
- **Include Directives**: Include other config files
- **Conditional Configuration**: Environment-based config sections
- **Config Templates**: Parameterized configuration templates

## 📊 Benefits

### Developer Experience
- **Reduced Command Line Complexity**: Long configurations in files
- **Reproducible Testing**: Version-controlled configurations
- **Team Collaboration**: Shared configuration standards
- **Environment Management**: Easy environment switching

### Operational Benefits
- **Consistency**: Standardized configurations across environments
- **Maintainability**: Centralized configuration management
- **Flexibility**: Easy parameter tuning without code changes
- **Documentation**: Self-documenting configuration files

This configuration file support significantly enhances stress_tool's usability and makes it more suitable for production environments and team workflows.
