# stress_tool

A high-performance stress testing tool specifically designed for Large Language Model (LLM) and multimodal model inference services. This tool provides comprehensive performance analysis including latency, throughput, token-level metrics, and multimodal processing capabilities.

## Features

- **🚀 High Performance**: Built with Rust for maximum performance and minimal overhead
- **🔄 Concurrent Testing**: Configurable concurrency levels for realistic load simulation
- **📊 Comprehensive Metrics**: Detailed latency, throughput, and token-level statistics
- **🌊 Streaming Support**: Full support for Server-Sent Events (SSE) streaming responses
- **🖼️ Multimodal Support**: Test vision models with images and videos
- **📈 Real-time Monitoring**: Live progress updates and performance metrics
- **🎯 Flexible Datasets**: Support for various dataset formats including JSONL
- **⚡ Token Estimation**: Local tokenizer for accurate token counting

## Installation

### From Source

```bash
git clone https://github.com/your-org/stress_tool.git
cd stress_tool
cargo build --release
```

### Prerequisites

- Rust 1.70+ 
- For multimodal features: image processing libraries

## Quick Start

### Basic Text Model Testing

```bash
# Test with default prompts
./stress_tool --url http://localhost:8000/v1/chat/completions \
              --model gpt-3.5-turbo \
              --concurrency 4 \
              --number 100

# Test with custom dataset
./stress_tool --url http://localhost:8000/v1/chat/completions \
              --model gpt-3.5-turbo \
              --dataset my_prompts.jsonl \
              --concurrency 8 \
              --number 1000
```

### Multimodal Model Testing

```bash
# Test vision model with images
./stress_tool --multimodal \
              --url http://localhost:8000/v1/chat/completions \
              --model qwen2.5-vl-32b-instruct \
              --dataset multimodal_dataset.jsonl \
              --image-base-path ./test_images \
              --concurrency 2 \
              --number 50
```

### Streaming Mode

```bash
# Test with streaming enabled
./stress_tool --url http://localhost:8000/v1/chat/completions \
              --model gpt-3.5-turbo \
              --stream \
              --concurrency 4 \
              --number 100
```

## Command Line Options

```
Usage: stress_tool [OPTIONS] --url <URL>

Options:
  -m, --model <MODEL>                      Model name [default: gpt-3.5-turbo]
  -u, --url <URL>                          API endpoint URL
  -c, --concurrency <CONCURRENCY>          Number of concurrent requests [default: 1]
  -n, --number <NUMBER>                    Total number of requests to send [default: 100]
  -d, --dataset <DATASET>                  Dataset file path
  -t, --max-tokens <MAX_TOKENS>            Maximum tokens to generate [default: 512]
      --temperature <TEMPERATURE>          Sampling temperature [default: 0.1]
      --stream                             Enable streaming mode
      --timeout <TIMEOUT>                  Request timeout in seconds [default: 30]
  -o, --output <OUTPUT>                    Output directory [default: benchmark]
      --multimodal                         Enable multimodal mode
      --image-base-path <IMAGE_BASE_PATH>  Base path for image files
      --video-base-path <VIDEO_BASE_PATH>  Base path for video files
      --max-image-size <MAX_IMAGE_SIZE>    Maximum image size in pixels [default: 1024]
      --image-quality <IMAGE_QUALITY>      Image quality (1-100) [default: 85]
      --api-key <API_KEY>                  API key for authentication
  -v, --verbose                            Verbose logging
  -h, --help                               Print help
```

## Dataset Formats

### Text Dataset (JSONL)

```jsonl
{"prompt": "What is the capital of France?", "expected_tokens": 10}
{"prompt": "Explain quantum computing", "expected_tokens": 200}
```

### OpenAI Chat Format

```jsonl
{
  "messages": [
    {"role": "system", "content": "You are a helpful assistant."},
    {"role": "user", "content": "What is machine learning?"}
  ],
  "expected_tokens": 150
}
```

### Multimodal Dataset

```jsonl
{"_config": {"image_base_path": "./images", "default_image_quality": 85}}
{
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "Describe this image"},
        {"type": "image", "path": "sample.jpg"}
      ]
    }
  ],
  "expected_tokens": 100
}
```

## Output Metrics

The tool generates comprehensive performance reports including:

- **Request Statistics**: Success/failure counts, total requests
- **Latency Metrics**: Average, P50, P90, P95, P99 latencies
- **Throughput**: QPS (queries per second), tokens per second
- **Streaming Metrics**: Time to first token, per-token generation time
- **Multimodal Metrics**: Image processing time, data transfer volumes
- **Token Usage**: Prompt tokens, completion tokens, total tokens

See [METRICS.md](METRICS.md) for detailed output format specification.

## Examples

### Testing Qwen2.5-VL Model

```bash
# Create test dataset
echo '{"_config": {"image_base_path": "./test_images"}}' > test.jsonl
echo '{"messages": [{"role": "user", "content": [{"type": "text", "text": "What do you see?"}, {"type": "image", "path": "image1.jpg"}]}], "expected_tokens": 50}' >> test.jsonl

# Run multimodal test
./stress_tool --multimodal \
              --url https://your-api-endpoint/v1/chat/completions \
              --model Qwen/Qwen2.5-VL-32B-Instruct \
              --dataset test.jsonl \
              --concurrency 2 \
              --number 10
```

### Performance Tuning

```bash
# High concurrency test
./stress_tool --url http://localhost:8000/v1/chat/completions \
              --model your-model \
              --concurrency 16 \
              --number 1000 \
              --timeout 60

# Long context test
./stress_tool --url http://localhost:8000/v1/chat/completions \
              --model your-model \
              --max-tokens 2048 \
              --temperature 0.7 \
              --dataset long_context.jsonl
```

### Using Configuration Files

```bash
# Create a configuration file
cat > my_config.yaml << EOF
model: "Qwen/Qwen2.5-VL-32B-Instruct"
api_base: "https://d07161945-vllm-openaiv092-463-c4nsjzqg-8000.550c.cloud/v1/chat/completions"
concurrency: 4
total_requests: 50
multimodal: true
dataset: "examples/datasets/simple_multimodal_test.jsonl"
image_base_path: "./test_images"
verbose: true
EOF

# Run with config file
./stress_tool --config-file my_config.yaml

# Override specific settings
./stress_tool --config-file my_config.yaml --concurrency 8 --number 100
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Built with [Rust](https://www.rust-lang.org/)
- Uses [reqwest](https://github.com/seanmonstar/reqwest) for HTTP client
- Image processing with [image](https://github.com/image-rs/image) crate
- CLI powered by [clap](https://github.com/clap-rs/clap)
