# stress_tool 产品设计文档

## 1. 产品概述

### 1.1 产品定位
stress_tool 是一个专为大语言模型和多模态模型推理服务设计的性能压测工具，支持文本、图片、视频等多种输入模态，支持流式和非流式场景，提供详细的性能指标分析。

### 1.2 核心价值
- **专业性**：针对 LLM 和多模态模型推理服务的特殊需求（流式返回、token 计算、多媒体处理等）
- **准确性**：本地 tokenizer 预估，精确的延迟测量，多模态输入处理
- **易用性**：简单的命令行接口，丰富的输出报告
- **可扩展性**：支持多种数据集格式和配置选项，灵活的多模态输入组合

## 2. 功能需求

### 2.1 核心功能
- **并发压测**：支持可配置的并发数
- **流式支持**：完整支持 SSE 流式响应
- **多模态输入**：支持文本、图片、视频等多种输入模态的组合
- **多指标统计**：延迟、吞吐量、成功率等
- **失败分析**：详细的错误统计和日志

### 2.2 输入参数
```rust
pub struct Config {
    pub model: String,           // 模型名称
    pub api_base: String,        // API 基础地址
    pub concurrency: u32,        // 并发数
    pub total_requests: u32,     // 总请求数
    pub dataset: String,         // 数据集文件路径
    pub max_tokens: u32,         // 最大生成 token 数
    pub temperature: f32,        // 采样温度
    pub stream: bool,            // 是否流式
    pub timeout: u64,            // 请求超时时间（秒）

    // 多模态相关配置
    pub multimodal: bool,        // 是否启用多模态模式
    pub image_base_path: Option<String>, // 图片文件基础路径
    pub video_base_path: Option<String>, // 视频文件基础路径
    pub max_image_size: Option<u32>,     // 最大图片尺寸（像素）
    pub image_quality: Option<u8>,       // 图片质量（1-100）
}
```

### 2.3 输出指标
包括：
- 请求统计（成功/失败数量）
- 时间指标（延迟分布、TTFT、每 token 时间）
- 吞吐量指标（QPS、tokens/sec）
- 多模态指标（图片处理时间、视频处理时间、多媒体数据传输量）
- 系统资源快照

下面给出一份实际运行后生成的 JSON 结果文件示例，并在每个字段后面用注释形式说明：

```json
{
  /* ========== 测试整体信息 ========== */
  "test_id": "20250717143000",               // 测试批次号，自动生成，格式：YYYYMMDDHHMMSS
  "test_config": {                           // 本次压测的所有输入参数
    "model": "gpt-3.5-turbo",                // 被测模型名
    "api_base": "http://127.0.0.1:8000/v1",  // 推理服务地址
    "concurrency": 8,                        // 并发数（多少个协程/线程同时发请求）
    "total_requests": 1000,                  // 计划发出的总请求数
    "dataset": "openqa-500.jsonl",           // 使用的评测数据集
    "max_tokens": 512,                       // 每个请求生成的最大 token 数
    "temperature": 0.1,                      // 采样温度
    "stream": true                           // 是否开启流式返回
  },

  /* ========== 请求统计 ========== */
  "total_requests": 1000,          // 实际发出的请求数
  "succeed_requests": 998,         // HTTP 200 且返回 JSON 正常的请求
  "failed_requests": 2,            // 网络错误/超时/非 200 状态码/解析失败的请求
  "total_prompt_tokens": 245360,   // 所有请求的 prompt token 总数（由本地 tokenizer 预估）
  "total_completion_tokens": 478912,// 所有请求返回的 completion token 总数（由本地 tokenizer 预估）

  /* ========== 时间相关指标 ========== */
  "test_duration_seconds": 63.47,  // 从第 1 个请求发出到最后一个请求响应完成的总耗时
  "first_request_sent": "2025-07-17T14:30:01.123Z",
  "last_request_done":    "2025-07-17T14:31:04.593Z",

  /* ========== 吞吐量指标 ========== */
  "throughput_tokens_per_sec": 7546.8,   // = total_completion_tokens / test_duration_seconds
  "qps": 15.75,                          // = succeed_requests / test_duration_seconds

  /* ========== 延迟指标（毫秒） ========== */
  "average_latency_ms": 496.3,           // 所有成功请求“端到端”延迟的算术平均
                                          // 计算方式：对每个请求记录 (response_done - request_sent)
  "average_time_to_first_token_ms": 38.7,// 对流式返回，指从发送请求到收到首包 chunk 的用时
                                          // 非流式场景下该值与 average_latency_ms 相同
  "average_time_per_output_token_ms": 9.1, // = 平均生成阶段耗时 / 平均输出 token 数
                                           // 生成阶段耗时 = (last_chunk_time - first_chunk_time)
  "p50_latency_ms": 471,                 // 50% 请求延迟 ≤ 471 ms
  "p90_latency_ms": 612,                 // 90% 请求延迟 ≤ 612 ms
  "p95_latency_ms": 689,
  "p99_latency_ms": 812,

  /* ========== 输入/输出长度统计 ========== */
  "avg_prompt_tokens": 245.8,            // = total_prompt_tokens / succeed_requests
  "avg_completion_tokens": 479.9,        // = total_completion_tokens / succeed_requests

  /* ========== 失败统计 ========== */
  "failures": [                          // 仅列出前 10 条，便于排查；全量请查日志
    {
      "request_id": "req-000017",
      "error": "timeout",
      "elapsed_ms": 30002
    },
    {
      "request_id": "req-000234",
      "error": "HTTP 503",
      "elapsed_ms": 45
    }
  ],

  /* ========== 系统资源快照（可选） ========== */
  "system_info": {
    "cpu_count": 16,
    "memory_total_gb": 62.8,
    "gpu_count": 1,
    "gpu_memory_total_mb": 24576,
    "stress_tool_version": "0.4.2"
  },

  /* ========== 原始直方图（可选，用于重绘曲线） ========== */
  "latency_histogram": [                 // 毫秒级桶，每 50 ms 一个桶
    [0, 50, 0],
    [50, 100, 2],
    [100, 150, 5],
    ...
    [800, 850, 1]
  ]
}
```

---

### 指标是如何测得的（测试方法简述）

| 指标 | 采集位置 | 说明 |
|---|---|---|
| total_requests | 压测端计数器 | 每发一条 HTTP 请求就 +1 |
| succeed_requests | 收到 HTTP 200 且 JSON 解析成功 | 否则计入 failed_requests |
| total_prompt_tokens | 本地 tokenizer | 在发送前把 prompt 编码一次即可得到长度 |
| total_completion_tokens | 本地 tokenizer | 把返回的 `choices[0].text` 或 `delta.content` 拼起来再编码 |
| average_latency_ms | 压测端计时 | `time_end - time_start`，`time_start` 为请求发出瞬间，`time_end` 为收到最后一个字节（非流式）或收到最后一个 `finish_reason=stop` 的 chunk（流式） |
| average_time_to_first_token_ms | 流式场景 | 收到第一个非空 chunk 的时间 - `time_start` |
| average_time_per_output_token_ms | 流式场景 | `(last_chunk_time - first_chunk_time) / (completion_tokens - 1)` |
| throughput_tokens_per_sec | 计算得到 | 所有成功请求的 completion_tokens 之和 ÷ 总耗时 |
| qps | 计算得到 | succeed_requests ÷ 总耗时 |
| pXX_latency_ms | 排序后取分位 | 把所有成功请求的延迟放进数组，排序后取对应百分位 |



## 3. 技术架构

### 3.1 技术栈
- **语言**：Rust（高性能、内存安全）
- **异步运行时**：tokio（并发处理）
- **HTTP 客户端**：reqwest（支持 SSE）
- **JSON 处理**：serde_json
- **Tokenizer**：tiktoken-rs 或 tokenizers
- **图像处理**：image crate（图片加载、格式转换、尺寸调整）
- **视频处理**：ffmpeg-next 或 opencv（视频帧提取、格式转换）
- **Base64 编码**：base64 crate（多媒体数据编码）

### 3.2 核心模块
```
src/
├── main.rs              // 入口点和 CLI 解析
├── config.rs            // 配置管理
├── client.rs            // HTTP 客户端封装
├── tokenizer.rs         // Token 计算
├── metrics.rs           // 指标收集和统计
├── dataset.rs           // 数据集加载
├── runner.rs            // 压测执行器
├── reporter.rs          // 结果输出
└── multimodal/          // 多模态处理模块
    ├── mod.rs           // 模块入口
    ├── image.rs         // 图片处理（加载、转换、编码）
    ├── video.rs         // 视频处理（帧提取、编码）
    ├── request.rs       // 多模态请求构建
    └── utils.rs         // 多模态工具函数
```

### 3.3 数据流
1. 加载配置和数据集
2. 初始化 tokenizer 和 HTTP 客户端
3. 预处理多模态数据（图片/视频加载、格式转换、Base64编码）
4. 启动并发任务执行请求
5. 实时收集指标数据（包括多模态处理时间）
6. 生成统计报告和日志

## 4. 多模态功能设计

### 4.1 支持的输入模态
- **文本**：纯文本输入，与现有功能兼容
- **图片**：支持 JPEG、PNG、WebP 等常见格式
- **视频**：支持 MP4、AVI、MOV 等格式（提取关键帧）
- **混合模态**：文本+图片、文本+视频、文本+图片+视频等组合

### 4.2 数据集格式
#### 4.2.1 纯文本数据集（兼容现有格式）
```jsonl
{"prompt": "What is the capital of France?", "expected_tokens": 10}
{"prompt": "Explain quantum computing", "expected_tokens": 200}
```

#### 4.2.2 多模态数据集格式
```jsonl
{
  "messages": [
    {"role": "user", "content": [
      {"type": "text", "text": "Describe this image"},
      {"type": "image", "path": "images/sample1.jpg"}
    ]}
  ],
  "expected_tokens": 50
}
{
  "messages": [
    {"role": "user", "content": [
      {"type": "text", "text": "What happens in this video?"},
      {"type": "video", "path": "videos/sample1.mp4", "fps": 1.0}
    ]}
  ],
  "expected_tokens": 100
}
```

### 4.3 API 请求格式
支持 OpenAI Vision API 兼容格式：
```json
{
  "model": "gpt-4-vision-preview",
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "What's in this image?"},
        {"type": "image_url", "image_url": {"url": "data:image/jpeg;base64,..."}}
      ]
    }
  ],
  "max_tokens": 100
}
```

### 4.4 性能优化策略
- **图片预处理**：批量加载和缓存，避免重复处理
- **尺寸优化**：根据模型要求调整图片尺寸，减少传输量
- **并发处理**：多模态数据处理与网络请求并行
- **内存管理**：及时释放大型多媒体数据，避免内存泄漏

## 5. 用户体验设计

### 5.1 命令行接口
```bash
stress_tool [OPTIONS] --api-base <URL>

OPTIONS:
    -m, --model <MODEL>              模型名称 [default: gpt-3.5-turbo]
    -u, --api-base <URL>             API 端点 URL
    -c, --concurrency <NUM>          并发数 [default: 1]
    -n, --number <NUM>               总请求数 [default: 100]
    -d, --dataset <FILE>             数据集文件路径
    -t, --max-tokens <NUM>           最大生成 tokens [default: 512]
        --temperature <FLOAT>        采样温度 [default: 0.1]
        --stream                     启用流式模式
        --timeout <SECONDS>          请求超时时间 [default: 30]
    -o, --output <DIR>               输出目录 [default: benchmark]

    # 多模态相关选项
        --multimodal                 启用多模态模式
        --image-base-path <PATH>     图片文件基础路径
        --video-base-path <PATH>     视频文件基础路径
        --max-image-size <PIXELS>    最大图片尺寸 [default: 1024]
        --image-quality <QUALITY>    图片质量 1-100 [default: 85]

    -h, --help                       显示帮助信息
```

### 5.2 基本使用示例
```bash
# 基本文本压测
stress_tool --model gpt-3.5-turbo \
  --api-base https://api.openai.com/v1/chat/completions \
  --dataset text_dataset.jsonl \
  --concurrency 8 \
  --number 1000

# 流式压测
stress_tool --model gpt-3.5-turbo \
  --api-base https://api.openai.com/v1/chat/completions \
  --dataset text_dataset.jsonl \
  --stream \
  --concurrency 4 \
  --number 500
```

### 5.3 输出格式
- **实时进度**：显示当前进度和实时 QPS，包括多模态数据处理进度
- **结果文件**：JSON 格式的详细报告，包含多模态相关指标
- **日志文件**：包含所有请求的详细日志，记录多模态数据处理过程

### 5.4 多模态使用示例
```bash
# 图片压测
stress_tool --multimodal \
  --model gpt-4-vision-preview \
  --api-base https://api.example.com/v1/chat/completions \
  --dataset multimodal_images.jsonl \
  --image-base-path ./test_images \
  --concurrency 4 \
  --number 100

# 视频压测
stress_tool --multimodal \
  --model gpt-4-vision-preview \
  --api-base https://api.example.com/v1/chat/completions \
  --dataset multimodal_videos.jsonl \
  --video-base-path ./test_videos \
  --concurrency 2 \
  --number 50
```

## 6. 实现进度

### 6.1 当前版本状态（v0.1.0）
**实际实现进度：已超越 v0.3.0 阶段，接近 v0.4.0**

#### ✅ 已完成功能

**MVP 阶段功能（v0.1.0）**
- ✅ 基础 CLI 参数解析（完整实现，支持所有核心参数）
- ✅ HTTP 客户端实现（支持标准和流式请求）
- ✅ 并发压测（基于 tokio 的异步并发）
- ✅ 基础指标统计（延迟、QPS、成功率等）

**完善阶段功能（v0.2.0）**
- ✅ 流式响应支持（完整的 SSE 流式处理）
- ⚠️ Tokenizer 集成（依赖已配置，但缺少独立模块）
- ✅ 详细的延迟统计（P50/P90/P95/P99 分位数）
- ⚠️ 错误处理（基础实现，缺少重试机制）

**多模态阶段功能（v0.3.0）**
- ✅ 多模态数据集格式支持（JSONL 格式，支持图片和视频）
- ✅ 图片处理和编码（完整的图片加载、转换、Base64编码）
- ⚠️ 视频处理和帧提取（依赖已配置，但缺少 video.rs 模块）
- ✅ 多模态请求构建（OpenAI Vision API 兼容格式）
- ✅ 多模态指标统计（图片处理时间、数据传输量等）

**优化阶段功能（v0.4.0）**
- ✅ 配置文件支持（YAML/TOML 格式，CLI 参数覆盖）
- ✅ 性能优化（异步处理、内存管理、缓存机制）
- ✅ 更多数据集格式支持（文本和多模态数据集）
- ❌ 可视化报告（仅有 JSON/CSV/TXT 格式）

#### ⚠️ 发现的不一致问题

**1. 模块结构不一致**
- 设计文档要求：`src/tokenizer.rs` 和 `src/metrics.rs`
- 实际实现：功能分散在其他模块中，缺少独立模块

**2. CLI 参数不一致**
- 设计文档：`--api-base`
- 实际实现：`--url`（需要修正）

**3. 缺失的核心模块**
- `src/tokenizer.rs`：Token 计算模块
- `src/metrics.rs`：指标收集和统计模块
- `src/multimodal/video.rs`：视频处理模块

**4. 功能实现差异**
- 重试机制：设计要求但未实现
- 可视化报告：设计要求但未实现
- 单元测试：部分实现，覆盖率不足

### 6.2 待完成任务

#### 🔧 紧急修复（v0.1.1）
- [ ] 修正 CLI 参数：`--url` → `--api-base`
- [ ] 创建 `src/tokenizer.rs` 模块
- [ ] 创建 `src/metrics.rs` 模块
- [ ] 实现基础重试机制

#### 📹 视频功能完善（v0.1.2）
- [ ] 创建 `src/multimodal/video.rs` 模块
- [ ] 实现视频帧提取功能
- [ ] 添加视频格式支持（MP4、AVI、MOV）
- [ ] 完善视频处理指标统计

#### 🧪 测试和质量（v0.1.3）
- [ ] 增加单元测试覆盖率
- [ ] 添加集成测试
- [ ] 性能基准测试
- [ ] 错误处理测试

#### 📊 可视化和报告（v0.2.0）
- [ ] HTML 格式报告生成
- [ ] 延迟分布图表
- [ ] 实时监控界面
- [ ] 性能趋势分析

### 6.3 版本规划调整

基于实际实现进度，调整版本规划：

**v0.1.1（修复版本）** - 预计 1 周
- 修正设计文档不一致问题
- 补充缺失的核心模块
- 基础功能完善

**v0.1.2（视频功能）** - 预计 1 周
- 完整的视频处理支持
- 多模态功能完善

**v0.1.3（测试版本）** - 预计 1 周
- 测试覆盖率提升
- 质量保证完善

**v0.2.0（可视化版本）** - 预计 2 周
- 可视化报告功能
- 用户体验优化

## 7. 质量保证

### 7.1 测试策略
- 单元测试：各模块功能测试，包括多模态处理模块
- 集成测试：端到端压测流程，覆盖多模态场景
- 性能测试：工具本身的性能验证，多模态数据处理性能
- 兼容性测试：不同格式的图片、视频文件处理

### 7.2 错误处理
- 网络错误重试机制
- 优雅的超时处理
- 详细的错误日志记录
- 多模态数据处理错误处理（文件不存在、格式不支持等）

## 8. 部署和分发

### 8.1 构建方式
- 支持多平台编译（Linux、macOS、Windows）
- 提供预编译二进制文件
- 支持 Docker 容器化部署
- 多模态依赖库的静态链接或动态链接配置

### 8.2 配置管理
- 命令行参数优先级最高
- 支持配置文件（YAML/JSON）
- 环境变量支持
- 多模态相关配置的验证和默认值设置
