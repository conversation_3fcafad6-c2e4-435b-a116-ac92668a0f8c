{"rustc": 12610991425282158916, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 5347358027863023418, "path": 17293521362548010272, "deps": [[555019317135488525, "regex_automata", false, 10336092604098221498], [2779309023524819297, "aho_corasick", false, 3134528676589430871], [9408802513701742484, "regex_syntax", false, 16473331630951800361], [15932120279885307830, "memchr", false, 7327600526021063576]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-07c7d9baa7c24a1c/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}