{"rustc": 12610991425282158916, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 5347358027863023418, "path": 5382478860770802435, "deps": [[7312356825837975969, "crc32fast", false, 13480221537067077764], [7636735136738807108, "miniz_oxide", false, 11024597351457067245]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/flate2-3350298e80d2e095/dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}