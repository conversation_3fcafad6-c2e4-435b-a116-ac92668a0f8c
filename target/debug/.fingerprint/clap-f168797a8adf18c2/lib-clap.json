{"rustc": 12610991425282158916, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 9589772125425470163, "path": 12343595733943499647, "deps": [[1457576002496728321, "clap_derive", false, 6220487690890843170], [7361794428713524931, "clap_builder", false, 3855043821964046314]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-f168797a8adf18c2/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}