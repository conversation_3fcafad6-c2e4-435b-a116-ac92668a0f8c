{"rustc": 12610991425282158916, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 17669703692130904899, "path": 11937075844098266243, "deps": [[5103565458935487, "futures_io", false, 1267651553329289318], [1615478164327904835, "pin_utils", false, 11188666236240056754], [1811549171721445101, "futures_channel", false, 1126870045371719513], [1906322745568073236, "pin_project_lite", false, 9686756108126436180], [5451793922601807560, "slab", false, 8768889503286127841], [7013762810557009322, "futures_sink", false, 5506923647716545412], [7620660491849607393, "futures_core", false, 827287667379831129], [10565019901765856648, "futures_macro", false, 13294871270833111864], [15932120279885307830, "memchr", false, 7327600526021063576], [16240732885093539806, "futures_task", false, 2239797823426866790]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-0fc1e5b222ab7ad9/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}