{"rustc": 12610991425282158916, "features": "[\"__tls\", \"default\", \"default-tls\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"serde_json\", \"stream\", \"tokio-native-tls\", \"tokio-util\", \"wasm-streams\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 5347358027863023418, "path": 16734212365409761697, "deps": [[40386456601120721, "percent_encoding", false, 606453098931573627], [95042085696191081, "ipnet", false, 13681213082385495820], [264090853244900308, "sync_wrapper", false, 9361857728679641666], [784494742817713399, "tower_service", false, 15967957934878776142], [1288403060204016458, "tokio_util", false, 12712402453794013987], [1906322745568073236, "pin_project_lite", false, 9686756108126436180], [3150220818285335163, "url", false, 11154812513500876504], [3722963349756955755, "once_cell", false, 3970243602726193626], [4405182208873388884, "http", false, 17330739889158122736], [5986029879202738730, "log", false, 7308730225310622536], [7414427314941361239, "hyper", false, 13193307987062631365], [7620660491849607393, "futures_core", false, 827287667379831129], [8915503303801890683, "http_body", false, 12346800103511573978], [9689903380558560274, "serde", false, 11058388967974992583], [10229185211513642314, "mime", false, 9932233112451321390], [10629569228670356391, "futures_util", false, 10035899157131215108], [11107720164717273507, "system_configuration", false, 610971734211567933], [12186126227181294540, "tokio_native_tls", false, 12525190038757100967], [12367227501898450486, "hyper_tls", false, 9226905313723075603], [12393800526703971956, "tokio", false, 2397616842696045174], [13763625454224483636, "h2", false, 17306064466548918258], [14564311161534545801, "encoding_rs", false, 5194483423096849252], [15367738274754116744, "serde_json", false, 6328120173208708731], [16066129441945555748, "bytes", false, 1539245292653426879], [16311359161338405624, "rustls_pemfile", false, 6292002455688479311], [16542808166767769916, "serde_urlencoded", false, 18122134116862453906], [16785601910559813697, "native_tls_crate", false, 4605585183714611183], [18066890886671768183, "base64", false, 10633689666993953191]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/reqwest-5f1d8d9fb2efd6f5/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}