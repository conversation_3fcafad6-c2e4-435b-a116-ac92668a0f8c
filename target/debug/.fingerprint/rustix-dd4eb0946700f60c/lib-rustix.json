{"rustc": 12610991425282158916, "features": "[\"alloc\", \"default\", \"fs\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 13113447432260090560, "path": 7091902801157200386, "deps": [[4684437522915235464, "libc", false, 14081188813483983574], [7896293946984509699, "bitflags", false, 5421141422298692827], [8253628577145923712, "libc_errno", false, 6495187242504645013], [10004434995811528692, "build_script_build", false, 1416334329191569445]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-dd4eb0946700f60c/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}