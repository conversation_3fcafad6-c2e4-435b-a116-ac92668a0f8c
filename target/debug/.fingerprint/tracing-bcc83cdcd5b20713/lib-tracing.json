{"rustc": 12610991425282158916, "features": "[\"attributes\", \"default\", \"std\", \"tracing-attributes\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 1006155289083248400, "path": 946218741192612267, "deps": [[325572602735163265, "tracing_attributes", false, 8398597202420955816], [1906322745568073236, "pin_project_lite", false, 9686756108126436180], [3424551429995674438, "tracing_core", false, 8708321951980726653]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-bcc83cdcd5b20713/dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}