{"rustc": 12610991425282158916, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 17669703692130904899, "path": 4691140835454490397, "deps": [[5103565458935487, "futures_io", false, 1267651553329289318], [1811549171721445101, "futures_channel", false, 1126870045371719513], [7013762810557009322, "futures_sink", false, 5506923647716545412], [7620660491849607393, "futures_core", false, 827287667379831129], [10629569228670356391, "futures_util", false, 10035899157131215108], [12779779637805422465, "futures_executor", false, 18306923608316848645], [16240732885093539806, "futures_task", false, 2239797823426866790]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-f3c3d0424f2349bb/dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}