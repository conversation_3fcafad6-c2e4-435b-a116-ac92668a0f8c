{"rustc": 12610991425282158916, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 1006155289083248400, "path": 14687282574240691864, "deps": [[1009387600818341822, "matchers", false, 9991794838183259948], [1017461770342116999, "sharded_slab", false, 10863782752734544556], [1359731229228270592, "thread_local", false, 10010803639909416710], [3424551429995674438, "tracing_core", false, 8708321951980726653], [3666196340704888985, "smallvec", false, 5280470035470028952], [3722963349756955755, "once_cell", false, 3970243602726193626], [8606274917505247608, "tracing", false, 15172771344281522010], [8614575489689151157, "nu_ansi_term", false, 12525397649004023240], [9451456094439810778, "regex", false, 2647614639741080562], [10806489435541507125, "tracing_log", false, 7826852225149266449]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-subscriber-2b48ab9e91a0f04e/dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}